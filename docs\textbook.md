# Building Full-Stack Hardware Control Applications
## A Complete Guide to Modern Web-Based Industrial Control Systems

### Table of Contents
1. [Introduction](#introduction)
2. [Architecture Overview](#architecture-overview)
3. [Technology Stack Deep Dive](#technology-stack-deep-dive)
4. [Project Structure and Organization](#project-structure-and-organization)
5. [Backend Development with FastAPI](#backend-development-with-fastapi)
6. [Frontend Development with Vue.js](#frontend-development-with-vuejs)
7. [Hardware Integration Patterns](#hardware-integration-patterns)
8. [Testing and Development Practices](#testing-and-development-practices)
9. [Deployment and Production Considerations](#deployment-and-production-considerations)
10. [Step-by-Step Tutorial](#step-by-step-tutorial)

---

## Introduction

This textbook teaches you how to build modern, web-based control systems for industrial hardware. Using the Recoater HMI (Human Machine Interface) project as a real-world example, you'll learn to create full-stack applications that provide intuitive web interfaces for complex hardware systems.

### What You'll Learn
- **Full-stack web development** for hardware control
- **Real-time communication** between web interfaces and hardware
- **Professional software architecture** patterns
- **Testing strategies** for hardware-integrated applications
- **Deployment practices** for industrial environments

### Prerequisites
- Basic programming knowledge (any language)
- Understanding of web concepts (HTTP, JSON)
- Familiarity with command-line interfaces

---

## Architecture Overview

Modern hardware control applications follow a **three-tier architecture**:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Hardware      │
│   (Vue.js)      │◄──►│   (FastAPI)     │◄──►│     API         │
│                 │    │                 │    │                 │
│ • User Interface│    │ • Business Logic│    │ • Physical      │
│ • Real-time UI  │    │ • API Gateway   │    │   Control       │
│ • State Mgmt    │    │ • WebSocket Hub │    │ • Sensors       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Why This Architecture?

**Separation of Concerns**: Each layer has a specific responsibility:
- **Frontend**: User experience and interface
- **Backend**: Business logic and hardware abstraction
- **Hardware**: Physical control and sensing

**Scalability**: Easy to add new features, interfaces, or hardware
**Maintainability**: Changes in one layer don't affect others
**Testability**: Each layer can be tested independently

---

## Technology Stack Deep Dive

### Backend: FastAPI (Python)

**FastAPI** is a modern Python web framework designed for building APIs.

#### Why FastAPI?
```python
# Automatic API documentation
@app.get("/status")
async def get_status() -> Dict[str, Any]:
    """Get system status - automatically documented!"""
    return {"status": "running"}
```

**Key Features:**
- **Type hints**: Automatic validation and documentation
- **Async support**: Handle many concurrent requests
- **OpenAPI integration**: Auto-generated API docs
- **Fast performance**: Comparable to Node.js and Go

#### Core Concepts

**1. Path Operations (Routes)**
```python
@app.get("/items/{item_id}")  # HTTP GET to /items/123
async def read_item(item_id: int):
    return {"item_id": item_id}
```

**2. Request/Response Models**
```python
from pydantic import BaseModel

class Item(BaseModel):
    name: str
    price: float

@app.post("/items/")
async def create_item(item: Item):  # Automatic JSON validation
    return item
```

**3. Dependency Injection**
```python
def get_database():
    return Database()

@app.get("/users/")
async def read_users(db = Depends(get_database)):  # Automatic injection
    return db.get_users()
```

### Frontend: Vue.js 3

**Vue.js** is a progressive JavaScript framework for building user interfaces.

#### Why Vue.js?
- **Gentle learning curve**: Easy to start, powerful when needed
- **Reactive data**: UI automatically updates when data changes
- **Component-based**: Reusable, maintainable code
- **Excellent tooling**: Great developer experience

#### Core Concepts

**1. Reactive Data**
```javascript
import { ref, reactive } from 'vue'

// Reactive primitive
const count = ref(0)
count.value++  // UI automatically updates

// Reactive object
const state = reactive({
  name: 'John',
  age: 30
})
state.age++  // UI automatically updates
```

**2. Components**
```vue
<template>
  <div>
    <h1>{{ title }}</h1>
    <button @click="increment">Count: {{ count }}</button>
  </div>
</template>

<script>
export default {
  setup() {
    const title = ref('My App')
    const count = ref(0)

    const increment = () => count.value++

    return { title, count, increment }
  }
}
</script>
```

**3. State Management (Pinia)**
```javascript
// stores/counter.js
import { defineStore } from 'pinia'

export const useCounterStore = defineStore('counter', () => {
  const count = ref(0)
  const increment = () => count.value++

  return { count, increment }
})
```

### Communication Layer: WebSockets + HTTP

**HTTP**: Request-response for commands
**WebSockets**: Real-time bidirectional communication

```javascript
// HTTP for commands
await api.post('/axis/x/move', { distance: 10, speed: 5 })

// WebSocket for real-time updates
const ws = new WebSocket('ws://localhost:8000/ws')
ws.onmessage = (event) => {
  const data = JSON.parse(event.data)
  updateUI(data)
}
```

---

## Project Structure and Organization

A well-organized project structure is crucial for maintainability:

```
project/
├── backend/                 # Python backend
│   ├── app/                # FastAPI application
│   │   ├── __init__.py
│   │   ├── main.py         # Application entry point
│   │   ├── dependencies.py # Dependency injection
│   │   └── api/            # API route modules
│   │       ├── __init__.py
│   │       ├── status.py   # Status endpoints
│   │       └── axis.py     # Axis control endpoints
│   ├── services/           # Business logic layer
│   │   ├── __init__.py
│   │   ├── recoater_client.py      # Hardware client
│   │   └── mock_recoater_client.py # Development mock
│   ├── tests/              # Test suite
│   ├── requirements.txt    # Python dependencies
│   └── .env               # Environment configuration
├── frontend/               # Vue.js frontend
│   ├── src/
│   │   ├── main.js        # Application entry point
│   │   ├── App.vue        # Root component
│   │   ├── views/         # Page components
│   │   ├── components/    # Reusable components
│   │   ├── stores/        # State management
│   │   └── services/      # API communication
│   ├── tests/             # Frontend tests
│   ├── package.json       # Node.js dependencies
│   └── vite.config.js     # Build configuration
├── docs/                  # Documentation
│   ├── README.md
│   ├── architecture.md
│   └── textbook.md        # This file!
└── install_deps.bat       # Setup script
```

### Why This Structure?

**Backend Organization:**
- `app/`: FastAPI-specific code
- `services/`: Business logic, hardware communication
- `tests/`: Comprehensive test coverage

**Frontend Organization:**
- `views/`: Full-page components (routes)
- `components/`: Reusable UI pieces
- `stores/`: Application state management
- `services/`: API communication logic

**Benefits:**
- **Clear separation**: Easy to find related code
- **Scalable**: Easy to add new features
- **Team-friendly**: Multiple developers can work without conflicts

---

## Backend Development with FastAPI

### Application Structure

The backend follows a **layered architecture**:

```python
# app/main.py - Application entry point
from fastapi import FastAPI
from app.api import status, axis
from app.dependencies import initialize_recoater_client

app = FastAPI(title="Recoater HMI Backend")
app.include_router(status.router, prefix="/api/v1")
app.include_router(axis.router, prefix="/api/v1")

@app.on_event("startup")
async def startup():
    initialize_recoater_client()
```

### Dependency Injection Pattern

**Problem**: How do we share the hardware client across all API endpoints without circular imports?

**Solution**: Centralized dependency injection

```python
# app/dependencies.py
from services.recoater_client import RecoaterClient

_recoater_client = None

def initialize_recoater_client():
    global _recoater_client
    _recoater_client = RecoaterClient("http://hardware:8080")

def get_recoater_client() -> RecoaterClient:
    if _recoater_client is None:
        raise HTTPException(503, "Client not initialized")
    return _recoater_client
```

**Usage in API endpoints:**
```python
# app/api/axis.py
from fastapi import Depends
from app.dependencies import get_recoater_client

@router.post("/axis/{axis}/move")
async def move_axis(
    axis: str,
    motion_data: MotionRequest,
    client = Depends(get_recoater_client)  # Automatic injection
):
    return client.move_axis(axis, motion_data.distance, motion_data.speed)
```

### Request/Response Models with Pydantic

**Pydantic** provides automatic validation and documentation:

```python
from pydantic import BaseModel, Field

class MotionRequest(BaseModel):
    distance: float = Field(..., description="Distance in mm")
    speed: float = Field(gt=0, description="Speed in mm/s")
    mode: str = Field(default="relative", regex="^(relative|absolute)$")

# Usage
@app.post("/move")
async def move(motion: MotionRequest):
    # motion.distance is guaranteed to be a float
    # motion.speed is guaranteed to be > 0
    # motion.mode is guaranteed to be "relative" or "absolute"
    return {"status": "moving", "params": motion.dict()}
```

### Error Handling Strategy

**Layered error handling** provides clear error messages:

```python
# services/recoater_client.py
class RecoaterConnectionError(Exception):
    """Hardware connection failed"""
    pass

class RecoaterAPIError(Exception):
    """Hardware returned error"""
    pass

# app/api/axis.py
@router.post("/move")
async def move_axis(motion: MotionRequest, client = Depends(get_recoater_client)):
    try:
        result = client.move_axis(motion.distance, motion.speed)
        return {"success": True, "result": result}
    except RecoaterConnectionError as e:
        raise HTTPException(503, f"Hardware connection failed: {e}")
    except RecoaterAPIError as e:
        raise HTTPException(502, f"Hardware error: {e}")
    except Exception as e:
        raise HTTPException(500, f"Internal error: {e}")
```

### WebSocket Real-time Communication

**WebSockets** enable real-time updates without polling:

```python
# Connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    async def broadcast(self, message: dict):
        for connection in self.active_connections:
            await connection.send_json(message)

manager = ConnectionManager()

# WebSocket endpoint
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            await websocket.receive_text()  # Keep alive
    except WebSocketDisconnect:
        manager.disconnect(websocket)

# Background task for status updates
async def status_polling_task():
    while True:
        status = get_hardware_status()
        await manager.broadcast({"type": "status", "data": status})
        await asyncio.sleep(1.0)
```

### Hardware Abstraction Layer

**Abstract hardware complexity** behind a clean interface:

```python
# services/recoater_client.py
class RecoaterClient:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.session = requests.Session()

    def _make_request(self, method: str, endpoint: str, **kwargs):
        """Centralized request handling with error management"""
        url = f"{self.base_url}/{endpoint}"
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            return response.json()
        except requests.ConnectionError as e:
            raise RecoaterConnectionError(f"Connection failed: {e}")
        except requests.HTTPError as e:
            raise RecoaterAPIError(f"API error: {e}")

    def get_state(self) -> dict:
        """Get current hardware state"""
        return self._make_request("GET", "/state")

    def move_axis(self, axis: str, distance: float, speed: float) -> dict:
        """Move hardware axis"""
        payload = {"distance": distance, "speed": speed}
        return self._make_request("POST", f"/axis/{axis}/move", json=payload)
```

### Development vs Production

**Mock client** for development without hardware:

```python
# services/mock_recoater_client.py
class MockRecoaterClient:
    """Simulates hardware for development"""

    def __init__(self, base_url: str):
        self.base_url = base_url
        self._state = {"status": "idle", "position": 0.0}

    def get_state(self) -> dict:
        return self._state.copy()

    def move_axis(self, axis: str, distance: float, speed: float) -> dict:
        # Simulate movement
        self._state["position"] += distance
        return {"success": True, "new_position": self._state["position"]}

# app/dependencies.py
def initialize_recoater_client():
    global _recoater_client
    if os.getenv("DEVELOPMENT_MODE") == "true":
        _recoater_client = MockRecoaterClient(base_url)
    else:
        _recoater_client = RecoaterClient(base_url)
```

---

## Frontend Development with Vue.js

### Component Architecture

Vue.js applications are built with **reusable components**:

```vue
<!-- components/AxisControl.vue -->
<template>
  <div class="axis-control">
    <h3>{{ axisName }} Axis</h3>
    <div class="status">
      Position: {{ position.toFixed(2) }}mm
      Status: {{ isMoving ? 'Moving' : 'Stopped' }}
    </div>
    <div class="controls">
      <button @click="move(-distance)" :disabled="isMoving">←</button>
      <input v-model.number="distance" type="number" step="0.1" />
      <button @click="move(distance)" :disabled="isMoving">→</button>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useStatusStore } from '@/stores/status'
import apiService from '@/services/api'

export default {
  name: 'AxisControl',
  props: {
    axisName: { type: String, required: true },
    axis: { type: String, required: true }  // 'x' or 'z'
  },
  setup(props) {
    const statusStore = useStatusStore()
    const distance = ref(10.0)

    // Computed properties automatically update when store changes
    const position = computed(() =>
      statusStore.axisData?.[props.axis]?.position || 0
    )
    const isMoving = computed(() =>
      statusStore.axisData?.[props.axis]?.running || false
    )

    const move = async (dist) => {
      try {
        await apiService.moveAxis(props.axis, {
          distance: dist,
          speed: 5.0,
          mode: 'relative'
        })
      } catch (error) {
        console.error('Move failed:', error)
      }
    }

    return { distance, position, isMoving, move }
  }
}
</script>
```

### State Management with Pinia

**Centralized state** keeps data consistent across components:

```javascript
// stores/status.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useStatusStore = defineStore('status', () => {
  // State
  const isConnected = ref(false)
  const statusData = ref(null)
  const axisData = ref(null)
  const error = ref(null)

  // Getters (computed properties)
  const systemStatus = computed(() =>
    statusData.value?.state || 'unknown'
  )

  const axisPosition = computed(() => (axis) =>
    axisData.value?.[axis]?.position || 0
  )

  // Actions (methods)
  const updateStatus = (newStatus) => {
    statusData.value = newStatus
    isConnected.value = true
    error.value = null
  }

  const updateAxisData = (newAxisData) => {
    axisData.value = newAxisData
  }

  const setError = (errorMessage) => {
    error.value = errorMessage
    isConnected.value = false
  }

  return {
    // State
    isConnected,
    statusData,
    axisData,
    error,
    // Getters
    systemStatus,
    axisPosition,
    // Actions
    updateStatus,
    updateAxisData,
    setError
  }
})
```

### API Service Layer

**Centralized API communication** with error handling:

```javascript
// services/api.js
import axios from 'axios'

const apiClient = axios.create({
  baseURL: '/api/v1',
  timeout: 5000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// Request interceptor for logging
apiClient.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => Promise.reject(error)
)

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

export default {
  // Status endpoints
  getStatus() {
    return apiClient.get('/status')
  },

  // Axis control endpoints
  moveAxis(axis, motionData) {
    return apiClient.post(`/axis/${axis}/motion`, motionData)
  },

  homeAxis(axis, homingData) {
    return apiClient.post(`/axis/${axis}/home`, homingData)
  },

  getAxisStatus(axis) {
    return apiClient.get(`/axis/${axis}`)
  }
}
```

### Real-time Updates with WebSockets

**WebSocket integration** for live data:

```javascript
// services/websocket.js
import { useStatusStore } from '@/stores/status'

class WebSocketService {
  constructor() {
    this.ws = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 1000
  }

  connect() {
    try {
      this.ws = new WebSocket('ws://localhost:8000/ws')

      this.ws.onopen = () => {
        console.log('WebSocket connected')
        this.reconnectAttempts = 0
      }

      this.ws.onmessage = (event) => {
        const data = JSON.parse(event.data)
        this.handleMessage(data)
      }

      this.ws.onclose = () => {
        console.log('WebSocket disconnected')
        this.reconnect()
      }

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error)
      }
    } catch (error) {
      console.error('WebSocket connection failed:', error)
      this.reconnect()
    }
  }

  handleMessage(data) {
    const statusStore = useStatusStore()

    switch (data.type) {
      case 'status_update':
        statusStore.updateStatus(data.data)
        if (data.axis_data) {
          statusStore.updateAxisData(data.axis_data)
        }
        break
      case 'connection_error':
        statusStore.setError(data.error)
        break
      default:
        console.log('Unknown message type:', data.type)
    }
  }

  reconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`Reconnecting... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
      setTimeout(() => this.connect(), this.reconnectInterval)
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }
}

export default new WebSocketService()
```

### Vue Router for Navigation

**Single Page Application** routing:

```javascript
// router/index.js
import { createRouter, createWebHistory } from 'vue-router'
import StatusView from '@/views/StatusView.vue'
import AxisView from '@/views/AxisView.vue'
import RecoaterView from '@/views/RecoaterView.vue'

const routes = [
  { path: '/', redirect: '/status' },
  { path: '/status', component: StatusView },
  { path: '/axis', component: AxisView },
  { path: '/recoater', component: RecoaterView }
]

export default createRouter({
  history: createWebHistory(),
  routes
})
```

---

## Hardware Integration Patterns

### API Design Principles

**RESTful API design** for hardware control:

```
GET    /api/v1/status           # Get system status
GET    /api/v1/axis/x           # Get X-axis status
POST   /api/v1/axis/x/motion    # Move X-axis
DELETE /api/v1/axis/x/motion    # Stop X-axis motion
PUT    /api/v1/config           # Update configuration
```

**Key principles:**
- **Nouns for resources**: `/axis/x` not `/moveAxis`
- **HTTP verbs for actions**: `POST` to create, `PUT` to update
- **Consistent naming**: Use same patterns throughout
- **Hierarchical structure**: `/axis/x/motion` shows relationship

### Error Handling Strategy

**Layered error handling** provides clear feedback:

```python
# Hardware layer errors
class HardwareError(Exception):
    pass

class ConnectionError(HardwareError):
    pass

class CommandError(HardwareError):
    pass

# API layer error mapping
@app.exception_handler(ConnectionError)
async def connection_error_handler(request, exc):
    return JSONResponse(
        status_code=503,
        content={"error": "Hardware connection failed", "detail": str(exc)}
    )

@app.exception_handler(CommandError)
async def command_error_handler(request, exc):
    return JSONResponse(
        status_code=400,
        content={"error": "Invalid command", "detail": str(exc)}
    )
```

### Development vs Production

**Environment-specific behavior**:

```python
# Development: Mock hardware
if os.getenv("DEVELOPMENT_MODE") == "true":
    hardware_client = MockHardwareClient()
else:
    hardware_client = RealHardwareClient()

# Mock client simulates real behavior
class MockHardwareClient:
    def __init__(self):
        self.position = 0.0
        self.moving = False

    def move_axis(self, distance, speed):
        self.moving = True
        # Simulate movement time
        time.sleep(abs(distance) / speed)
        self.position += distance
        self.moving = False
        return {"success": True, "position": self.position}
```

### Real-time Communication Patterns

**WebSocket message types**:

```javascript
// Status updates
{
  "type": "status_update",
  "data": {
    "timestamp": 1640995200,
    "connected": true,
    "state": {"status": "running"},
    "axis": {"x": {"position": 10.5, "moving": false}}
  }
}

// Error notifications
{
  "type": "error",
  "data": {
    "timestamp": 1640995200,
    "severity": "warning",
    "message": "Axis limit reached",
    "component": "x_axis"
  }
}

// Command acknowledgments
{
  "type": "command_ack",
  "data": {
    "command_id": "move_x_123",
    "status": "accepted",
    "estimated_duration": 5.0
  }
}
```

---

## Testing and Development Practices

### Backend Testing with pytest

**Comprehensive test coverage**:

```python
# tests/test_axis_api.py
import pytest
from fastapi.testclient import TestClient
from unittest.mock import patch
from app.main import app

client = TestClient(app)

class TestAxisAPI:
    @patch('app.dependencies.get_recoater_client')
    def test_move_axis_success(self, mock_client):
        # Arrange
        mock_client.return_value.move_axis.return_value = {"success": True}

        # Act
        response = client.post("/api/v1/axis/x/motion", json={
            "distance": 10.0,
            "speed": 5.0,
            "mode": "relative"
        })

        # Assert
        assert response.status_code == 200
        assert response.json()["success"] == True
        mock_client.return_value.move_axis.assert_called_once_with(
            "x", 10.0, 5.0, "relative"
        )

    @patch('app.dependencies.get_recoater_client')
    def test_move_axis_connection_error(self, mock_client):
        # Arrange
        mock_client.return_value.move_axis.side_effect = ConnectionError("Hardware offline")

        # Act
        response = client.post("/api/v1/axis/x/motion", json={
            "distance": 10.0,
            "speed": 5.0,
            "mode": "relative"
        })

        # Assert
        assert response.status_code == 503
        assert "Hardware connection failed" in response.json()["error"]
```

### Frontend Testing with Vitest

**Component testing**:

```javascript
// tests/AxisControl.test.js
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import AxisControl from '@/components/AxisControl.vue'
import apiService from '@/services/api'

// Mock API service
vi.mock('@/services/api', () => ({
  default: {
    moveAxis: vi.fn()
  }
}))

describe('AxisControl', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('renders axis name and position', () => {
    const wrapper = mount(AxisControl, {
      props: { axisName: 'X Axis', axis: 'x' }
    })

    expect(wrapper.text()).toContain('X Axis')
    expect(wrapper.text()).toContain('Position:')
  })

  it('calls API when move button clicked', async () => {
    apiService.moveAxis.mockResolvedValue({ data: { success: true } })

    const wrapper = mount(AxisControl, {
      props: { axisName: 'X Axis', axis: 'x' }
    })

    // Set distance
    await wrapper.find('input[type="number"]').setValue('5.0')

    // Click move right button
    await wrapper.findAll('button')[1].trigger('click')

    expect(apiService.moveAxis).toHaveBeenCalledWith('x', {
      distance: 5.0,
      speed: 5.0,
      mode: 'relative'
    })
  })
})
```

### Integration Testing

**End-to-end testing** with real API calls:

```python
# tests/test_integration.py
import pytest
import asyncio
from fastapi.testclient import TestClient
from app.main import app

class TestIntegration:
    def test_full_axis_control_flow(self):
        client = TestClient(app)

        # 1. Check initial status
        response = client.get("/api/v1/status")
        assert response.status_code == 200

        # 2. Move axis
        response = client.post("/api/v1/axis/x/motion", json={
            "distance": 10.0,
            "speed": 5.0,
            "mode": "relative"
        })
        assert response.status_code == 200

        # 3. Check axis status
        response = client.get("/api/v1/axis/x")
        assert response.status_code == 200

        # 4. Stop motion
        response = client.delete("/api/v1/axis/x/motion")
        assert response.status_code == 200
```

---

## Step-by-Step Tutorial

### Prerequisites Setup

**1. Install Required Software**
```bash
# Python 3.8+ and Node.js 16+
python --version  # Should be 3.8+
node --version    # Should be 16+
npm --version
```

**2. Create Project Structure**
```bash
mkdir hardware-control-app
cd hardware-control-app
mkdir backend frontend docs
```

### Backend Development

**Step 1: Initialize Python Project**
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install fastapi uvicorn requests python-dotenv pydantic
```

**Step 2: Create Basic FastAPI App**
```python
# backend/main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title="Hardware Control API")

# Enable CORS for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Hardware Control API"}

@app.get("/status")
async def get_status():
    return {"status": "running", "timestamp": "2024-01-01T00:00:00Z"}
```

**Step 3: Add Hardware Client**
```python
# backend/hardware_client.py
import requests
from typing import Dict, Any

class HardwareClient:
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.session = requests.Session()

    def get_status(self) -> Dict[str, Any]:
        # In real implementation, this would call actual hardware
        return {"connected": True, "status": "idle"}

    def move_axis(self, axis: str, distance: float, speed: float) -> Dict[str, Any]:
        # In real implementation, this would send command to hardware
        return {"success": True, "axis": axis, "distance": distance}

# Mock for development
class MockHardwareClient(HardwareClient):
    def __init__(self):
        self.position = {"x": 0.0, "y": 0.0, "z": 0.0}

    def move_axis(self, axis: str, distance: float, speed: float) -> Dict[str, Any]:
        self.position[axis] += distance
        return {
            "success": True,
            "axis": axis,
            "new_position": self.position[axis]
        }
```

**Step 4: Add API Endpoints**
```python
# backend/main.py (updated)
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from hardware_client import MockHardwareClient

app = FastAPI(title="Hardware Control API")
hardware = MockHardwareClient()

class MoveRequest(BaseModel):
    distance: float
    speed: float

@app.post("/axis/{axis}/move")
async def move_axis(axis: str, request: MoveRequest):
    try:
        result = hardware.move_axis(axis, request.distance, request.speed)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

**Step 5: Run Backend**
```bash
uvicorn main:app --reload --port 8000
# Visit http://localhost:8000/docs for API documentation
```

### Frontend Development

**Step 1: Initialize Vue.js Project**
```bash
cd ../frontend
npm create vue@latest .
# Choose: TypeScript: No, Router: Yes, Pinia: Yes, Testing: Vitest
npm install
npm install axios
```

**Step 2: Create API Service**
```javascript
// frontend/src/services/api.js
import axios from 'axios'

const apiClient = axios.create({
  baseURL: 'http://localhost:8000',
  timeout: 5000
})

export default {
  getStatus() {
    return apiClient.get('/status')
  },

  moveAxis(axis, distance, speed) {
    return apiClient.post(`/axis/${axis}/move`, { distance, speed })
  }
}
```

**Step 3: Create Axis Control Component**
```vue
<!-- frontend/src/components/AxisControl.vue -->
<template>
  <div class="axis-control">
    <h3>{{ axisName }}</h3>
    <div class="controls">
      <input v-model.number="distance" type="number" step="0.1" />
      <button @click="move(-distance)">←</button>
      <button @click="move(distance)">→</button>
    </div>
    <div v-if="status" class="status">{{ status }}</div>
  </div>
</template>

<script>
import { ref } from 'vue'
import api from '@/services/api'

export default {
  props: {
    axisName: String,
    axis: String
  },
  setup(props) {
    const distance = ref(10)
    const status = ref('')

    const move = async (dist) => {
      try {
        status.value = 'Moving...'
        const response = await api.moveAxis(props.axis, dist, 5.0)
        status.value = `Moved to ${response.data.new_position}`
      } catch (error) {
        status.value = `Error: ${error.message}`
      }
    }

    return { distance, status, move }
  }
}
</script>

<style scoped>
.axis-control {
  border: 1px solid #ccc;
  padding: 1rem;
  margin: 1rem;
  border-radius: 4px;
}
.controls {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}
</style>
```

**Step 4: Create Main View**
```vue
<!-- frontend/src/views/ControlView.vue -->
<template>
  <div class="control-view">
    <h1>Hardware Control</h1>
    <div class="status" v-if="systemStatus">
      System Status: {{ systemStatus.status }}
    </div>
    <div class="axes">
      <AxisControl axis-name="X Axis" axis="x" />
      <AxisControl axis-name="Y Axis" axis="y" />
      <AxisControl axis-name="Z Axis" axis="z" />
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import AxisControl from '@/components/AxisControl.vue'
import api from '@/services/api'

export default {
  components: { AxisControl },
  setup() {
    const systemStatus = ref(null)

    const loadStatus = async () => {
      try {
        const response = await api.getStatus()
        systemStatus.value = response.data
      } catch (error) {
        console.error('Failed to load status:', error)
      }
    }

    onMounted(loadStatus)

    return { systemStatus }
  }
}
</script>
```

**Step 5: Update Router**
```javascript
// frontend/src/router/index.js
import { createRouter, createWebHistory } from 'vue-router'
import ControlView from '@/views/ControlView.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/', component: ControlView }
  ]
})

export default router
```

**Step 6: Run Frontend**
```bash
npm run dev
# Visit http://localhost:5173
```

### Testing Your Application

**Step 1: Test Backend API**
```bash
# Test status endpoint
curl http://localhost:8000/status

# Test move endpoint
curl -X POST http://localhost:8000/axis/x/move \
  -H "Content-Type: application/json" \
  -d '{"distance": 10.0, "speed": 5.0}'
```

**Step 2: Test Frontend**
- Open browser to http://localhost:5173
- Try moving each axis using the controls
- Check browser console for any errors

### Deployment Considerations

**Production Setup:**
1. **Environment Variables**: Use `.env` files for configuration
2. **HTTPS**: Enable SSL/TLS for production
3. **Process Management**: Use systemd or Docker for service management
4. **Monitoring**: Add logging and health checks
5. **Security**: Implement authentication and authorization

**Docker Deployment:**
```dockerfile
# Dockerfile for backend
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

```dockerfile
# Dockerfile for frontend
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build
FROM nginx:alpine
COPY --from=0 /app/dist /usr/share/nginx/html
```

---

## Conclusion

You now have the knowledge to build professional hardware control applications using modern web technologies. The patterns and practices shown here scale from simple prototypes to complex industrial systems.

**Key Takeaways:**
- **Layered architecture** separates concerns and improves maintainability
- **Type safety** with Pydantic and TypeScript prevents runtime errors
- **Real-time communication** with WebSockets provides responsive UIs
- **Comprehensive testing** ensures reliability in production
- **Mock implementations** enable development without hardware

**Next Steps:**
- Add authentication and user management
- Implement data logging and analytics
- Add advanced error recovery mechanisms
- Scale to multiple hardware devices
- Implement advanced UI features like charts and dashboards

Happy building! 🚀