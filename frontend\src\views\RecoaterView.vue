<template>
  <div class="recoater-view p-6">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-3xl font-bold text-gray-900 mb-2">Recoater Control</h1>
      <p class="text-gray-600">Control drum motion, ejection, and suction pressure</p>
    </div>

    <!-- Connection Status -->
    <div class="mb-6">
      <div class="bg-white rounded-lg shadow-md p-4 border border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold text-gray-800">Connection Status</h2>
          <div class="flex items-center space-x-2">
            <div
              :class="[
                'w-3 h-3 rounded-full',
                statusStore.isConnected ? 'bg-green-500' : 'bg-red-500'
              ]"
            ></div>
            <span class="text-sm font-medium" :class="statusStore.isConnected ? 'text-green-600' : 'text-red-600'">
              {{ statusStore.isConnected ? 'Connected' : 'Disconnected' }}
            </span>
          </div>
        </div>
        <div v-if="statusStore.lastError" class="mt-2 text-sm text-red-600">
          {{ statusStore.lastError }}
        </div>
      </div>
    </div>

    <!-- Drums Grid -->
    <div v-if="statusStore.drumData && Object.keys(statusStore.drumData).length > 0" class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
      <DrumControl
        v-for="(drumInfo, drumId) in statusStore.drumData"
        :key="drumId"
        :drum-id="parseInt(drumId)"
        :drum-status="drumInfo?.info || {}"
        :motion-data="drumInfo?.motion || {}"
        :ejection-data="drumInfo?.ejection || {}"
        :suction-data="drumInfo?.suction || {}"
        :connected="statusStore.isConnected"
        @error="handleDrumError"
        @motion-started="handleMotionStarted"
        @motion-cancelled="handleMotionCancelled"
        @pressure-set="handlePressureSet"
      />
    </div>

    <!-- No Drums Available -->
    <div v-else-if="statusStore.isConnected && (!statusStore.drumData || Object.keys(statusStore.drumData).length === 0)" class="text-center py-12">
      <div class="bg-white rounded-lg shadow-md p-8 border border-gray-200">
        <div class="text-gray-500 mb-4">
          <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No Drums Available</h3>
        <p class="text-gray-600">No drum data is currently available from the recoater.</p>
      </div>
    </div>

    <!-- Loading State -->
    <div v-else class="text-center py-12">
      <div class="bg-white rounded-lg shadow-md p-8 border border-gray-200">
        <div class="text-gray-500 mb-4">
          <svg class="w-12 h-12 mx-auto animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">Loading Drum Data</h3>
        <p class="text-gray-600">Connecting to recoater and retrieving drum information...</p>
      </div>
    </div>

    <!-- Global Error Display -->
    <div v-if="globalError" class="fixed bottom-4 right-4 max-w-md">
      <div class="bg-red-50 border border-red-200 rounded-lg p-4 shadow-lg">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Error</h3>
            <div class="mt-1 text-sm text-red-700">{{ globalError }}</div>
          </div>
          <div class="ml-auto pl-3">
            <button @click="globalError = ''" class="text-red-400 hover:text-red-600">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Success Notifications -->
    <div v-if="successMessage" class="fixed bottom-4 right-4 max-w-md">
      <div class="bg-green-50 border border-green-200 rounded-lg p-4 shadow-lg">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-green-800">Success</h3>
            <div class="mt-1 text-sm text-green-700">{{ successMessage }}</div>
          </div>
          <div class="ml-auto pl-3">
            <button @click="successMessage = ''" class="text-green-400 hover:text-green-600">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import { useStatusStore } from '../stores/status'
import DrumControl from '../components/DrumControl.vue'

export default {
  name: 'RecoaterView',
  components: {
    DrumControl
  },
  setup() {
    const statusStore = useStatusStore()
    const globalError = ref('')
    const successMessage = ref('')

    // Auto-clear messages
    watch(globalError, (newError) => {
      if (newError) {
        setTimeout(() => {
          globalError.value = ''
        }, 5000)
      }
    })

    watch(successMessage, (newMessage) => {
      if (newMessage) {
        setTimeout(() => {
          successMessage.value = ''
        }, 3000)
      }
    })

    // Event handlers
    const handleDrumError = (error) => {
      console.error('Drum control error:', error)
      globalError.value = error.response?.data?.detail || 'Drum control operation failed'
    }

    const handleMotionStarted = (event) => {
      console.log('Motion started:', event)
      successMessage.value = `Motion started for Drum ${event.drumId}`
    }

    const handleMotionCancelled = (event) => {
      console.log('Motion cancelled:', event)
      successMessage.value = `Motion cancelled for Drum ${event.drumId}`
    }

    const handlePressureSet = (event) => {
      console.log('Pressure set:', event)
      successMessage.value = `${event.type} pressure set for Drum ${event.drumId}`
    }

    // Lifecycle
    onMounted(() => {
      // Connect to WebSocket for real-time updates
      statusStore.connectWebSocket()
    })

    onUnmounted(() => {
      // Clean up WebSocket connection
      statusStore.disconnectWebSocket()
    })

    return {
      statusStore,
      globalError,
      successMessage,
      handleDrumError,
      handleMotionStarted,
      handleMotionCancelled,
      handlePressureSet
    }
  }
}
</script>

<style scoped>
.recoater-view {
  min-height: 100vh;
  background-color: #f9fafb;
}
</style>
